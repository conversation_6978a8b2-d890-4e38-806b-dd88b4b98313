const http = require('http');
const fs = require('fs');
const url = require('url');

const PORT = 6666;

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    // 获取客户端IP地址
    const clientIP = req.headers['x-forwarded-for'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null);
    
    // 记录IP到文件
    const timestamp = new Date().toLocaleString('zh-CN');
    const logEntry = `${timestamp} - ${clientIP}\n`;
    
    fs.appendFile('ip_client.output', logEntry, 'utf8', (err) => {
        if (err) {
            console.error('写入文件时出错:', err);
        }
    });
    
    // 设置响应头
    res.writeHead(200, {
        'Content-Type': 'text/html; charset=utf-8'
    });
    
    // 返回简洁的HTML页面
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>🐖</title>
    </head>
    <body>
        <h1>🐖，被骗了吧</h1>
    </body>
    </html>
    `;
    
    res.end(html);
});

// 启动服务器
server.listen(PORT, '0.0.0.0', () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log(`局域网访问: http://0.0.0.0:${PORT}`);
});

// 错误处理
server.on('error', (err) => {
    console.error('服务器错误:', err);
});
