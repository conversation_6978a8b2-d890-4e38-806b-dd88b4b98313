from flask import Flask, request
import datetime

app = Flask(__name__)

@app.route('/')
def home():
    # 获取访问者IP
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    
    # 记录IP到文件
    with open('ip_client.output', 'a', encoding='utf-8') as f:
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        f.write(f'{timestamp} - {client_ip}\n')
    
    # 返回简洁的HTML页面
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>🐖</title>
    </head>
    <body>
        <h1>🐖，被骗了吧</h1>
    </body>
    </html>
    '''

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
